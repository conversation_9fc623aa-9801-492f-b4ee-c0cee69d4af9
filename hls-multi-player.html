<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HLS多路视频播放器</title>
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: #fff;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #4CAF50;
            margin-bottom: 10px;
        }

        .controls {
            background: #2d2d2d;
            padding: 20px;
            border-radius: 0px;
            margin-bottom: 20px;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .control-group select,
        .control-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #555;
            border-radius:5px;
            background: #3d3d3d;
            color: #fff;
        }

        .url-inputs {
            display: grid;
            gap: 10px;
            margin-top: 15px;
        }

        .url-input-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .url-input-group label {
            min-width: 80px;
            margin-bottom: 0;
        }

        .url-input-group input {
            flex: 1;
        }

        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius:5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #45a049;
        }

        .btn-secondary {
            background: #666;
        }

        .btn-secondary:hover {
            background: #777;
        }

        .video-grid {
            display: grid;
            gap: 0px;
            margin-top: 20px;
        }

        .video-grid.grid-1 {
            grid-template-columns: 1fr;
        }

        .video-grid.grid-2 {
            grid-template-columns: repeat(2, 1fr);
        }

        .video-grid.grid-3 {
            grid-template-columns: repeat(3, 1fr);
        }

        .video-grid.grid-4 {
            grid-template-columns: repeat(2, 1fr);
        }

        .video-grid.grid-5,
        .video-grid.grid-6 {
            grid-template-columns: repeat(3, 1fr);
        }

        .video-container {
            background: #2d2d2d;
            border-radius: 0px;
            overflow: hidden;
            position: relative;
        }

        .video-header {
            background: #3d3d3d;
            padding: 10px;
            font-size: 14px;
            font-weight: bold;
        }

        .video-wrapper {
            position: relative;
            width: 100%;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
        }

        .video-player {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #000;
        }

        .video-status {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            padding: 10px 20px;
            border-radius:5px;
            font-size: 14px;
        }

        .loading {
            color: #4CAF50;
        }

        .error {
            color: #f44336;
        }

        @media (max-width: 768px) {
            .video-grid.grid-2,
            .video-grid.grid-3,
            .video-grid.grid-4,
            .video-grid.grid-5,
            .video-grid.grid-6 {
                grid-template-columns: 1fr;
            }
            
            .url-input-group {
                flex-direction: column;
                align-items: stretch;
            }
            
            .url-input-group label {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- <div class="header">
            <h1>HLS多路视频播放器</h1>
            <p>支持同时播放多个HLS视频流</p>
        </div> -->

        <div class="controls">
            <div class="control-group">
                <label for="playerCount">播放窗口数量:</label>
                <select id="playerCount">
                    <option value="1">1个窗口</option>
                    <option value="2">2个窗口</option>
                    <option value="3">3个窗口</option>
                    <option value="4">4个窗口</option>
                    <option value="5">5个窗口</option>
                    <option value="6" selected>6个窗口</option>
                </select>
            </div>

            <div class="url-inputs" id="urlInputs">
                <!-- URL输入框将通过JavaScript动态生成 -->
            </div>

            <div style="margin-top: 20px; display: flex; gap: 10px;">
                <button class="btn" onclick="startAllPlayers()">开始播放</button>
                <button class="btn btn-secondary" onclick="stopAllPlayers()">停止播放</button>
            </div>
        </div>

        <div class="video-grid grid-6" id="videoGrid">
            <!-- 视频播放器将通过JavaScript动态生成 -->
        </div>
    </div>

    <script>
        class HLSMultiPlayer {
            constructor() {
                this.players = [];
                this.hlsInstances = [];
                this.playerCount = 6;
                this.init();
            }

            init() {
                this.updatePlayerCount();
                this.bindEvents();
            }

            bindEvents() {
                document.getElementById('playerCount').addEventListener('change', (e) => {
                    this.playerCount = parseInt(e.target.value);
                    this.updatePlayerCount();
                });
            }

            updatePlayerCount() {
                this.createUrlInputs();
                this.createVideoPlayers();
            }

            createUrlInputs() {
                const container = document.getElementById('urlInputs');
                container.innerHTML = '';

                for (let i = 0; i < this.playerCount; i++) {
                    const inputGroup = document.createElement('div');
                    inputGroup.className = 'url-input-group';
                    inputGroup.innerHTML = `
                        <label>视频${i + 1}:</label>
                        <input type="text" id="url${i}" placeholder="请输入HLS视频流地址 (http://example.com/stream.m3u8)" />
                    `;
                    container.appendChild(inputGroup);
                }
            }

            createVideoPlayers() {
                const container = document.getElementById('videoGrid');
                container.className = `video-grid grid-${this.playerCount}`;
                container.innerHTML = '';

                // 清理现有的HLS实例
                this.destroyAllPlayers();

                for (let i = 0; i < this.playerCount; i++) {
                    const videoContainer = document.createElement('div');
                    videoContainer.className = 'video-container';
                    videoContainer.innerHTML = `
                        <div class="video-header">视频播放器 ${i + 1}</div>
                        <div class="video-wrapper">
                            <video id="video${i}" class="video-player" controls muted></video>
                            <div class="video-status" id="status${i}">等待播放</div>
                        </div>
                    `;
                    container.appendChild(videoContainer);
                }
            }

            async startPlayer(index, url) {
                if (!url || !url.trim()) {
                    this.updateStatus(index, '请输入视频地址', 'error');
                    return;
                }

                const video = document.getElementById(`video${index}`);
                const statusEl = document.getElementById(`status${index}`);

                try {
                    this.updateStatus(index, '正在加载...', 'loading');

                    // 如果已有HLS实例，先销毁
                    if (this.hlsInstances[index]) {
                        this.hlsInstances[index].destroy();
                    }

                    if (Hls.isSupported()) {
                        const hls = new Hls({
                            enableWorker: false,
                            lowLatencyMode: true,
                        });

                        hls.loadSource(url);
                        hls.attachMedia(video);

                        hls.on(Hls.Events.MANIFEST_PARSED, () => {
                            statusEl.style.display = 'none';
                            video.play().catch(e => {
                                this.updateStatus(index, '播放失败: ' + e.message, 'error');
                            });
                        });

                        hls.on(Hls.Events.ERROR, (event, data) => {
                            if (data.fatal) {
                                this.updateStatus(index, `播放错误: ${data.type} - ${data.details}`, 'error');
                            }
                        });

                        this.hlsInstances[index] = hls;
                    } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                        // Safari原生支持
                        video.src = url;
                        video.addEventListener('loadedmetadata', () => {
                            statusEl.style.display = 'none';
                            video.play().catch(e => {
                                this.updateStatus(index, '播放失败: ' + e.message, 'error');
                            });
                        });
                    } else {
                        this.updateStatus(index, '浏览器不支持HLS播放', 'error');
                    }
                } catch (error) {
                    this.updateStatus(index, '加载失败: ' + error.message, 'error');
                }
            }

            stopPlayer(index) {
                const video = document.getElementById(`video${index}`);
                if (video) {
                    video.pause();
                    video.src = '';
                }

                if (this.hlsInstances[index]) {
                    this.hlsInstances[index].destroy();
                    this.hlsInstances[index] = null;
                }

                this.updateStatus(index, '已停止播放', '');
            }

            updateStatus(index, message, type = '') {
                const statusEl = document.getElementById(`status${index}`);
                if (statusEl) {
                    statusEl.textContent = message;
                    statusEl.className = `video-status ${type}`;
                    statusEl.style.display = message ? 'block' : 'none';
                }
            }

            destroyAllPlayers() {
                this.hlsInstances.forEach(hls => {
                    if (hls) {
                        hls.destroy();
                    }
                });
                this.hlsInstances = [];
            }
        }

        // 全局实例
        const multiPlayer = new HLSMultiPlayer();

        // 全局函数
        function startAllPlayers() {
            for (let i = 0; i < multiPlayer.playerCount; i++) {
                const url = document.getElementById(`url${i}`).value;
                if (url && url.trim()) {
                    multiPlayer.startPlayer(i, url);
                }
            }
        }

        function stopAllPlayers() {
            for (let i = 0; i < multiPlayer.playerCount; i++) {
                multiPlayer.stopPlayer(i);
            }
        }

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            multiPlayer.destroyAllPlayers();
        });
    </script>
</body>
</html>
